<?php

namespace common\queues;

/**
 * 推广定时充值
 *
 * Class RechargeJob
 */

use common\models\Config;
use common\helpers\BcHelper;
use common\models\backend\Member;
use Exception;
use services\common\FeishuExamineService;
use common\models\common\AdsAccountSub;
use common\components\promoteRecharge\PlatformFactory;
use common\enums\AdsTransferMoneyRecordIsAutoEnum;
use common\enums\AdsTransferMoneyRecordStatusEnum;
use common\models\common\AdsAccount;
use common\models\promote\AdsTransferMoneyRecord;
use Yii;
use GuzzleHttp\Client;

class RechargeJob extends BaseJob
{
    const AUTO_RECHARGE_AMOUNT = 50; // 自动充值金额
    const AUTO_RECHARGE_COUNT_LIMIT = 5; // 自动充值次数限制

    //延迟时间：单位秒，默认10
    public $delay = 10;
    //重试次数  
    public $retryTimes = 1;
    //最多可延长执行次数
    public $maxDelayTimes = 0;
    //数据
    public $data;
    //是否发送消息
    public $isSendMessage = true;

    public function execute($queue)
    {
        $data = $this->data;
        $arrError = [];

        try {
            try {
                $record = AdsTransferMoneyRecord::find()
                    ->where(['id' => $data['record_id']])
                    ->andWhere(['status' => AdsTransferMoneyRecordStatusEnum::PENDING])
                    ->count();

                if ($record) {
                    $platform = AdsAccountSub::getPlatformBySubAdvertiserId($data['target_advertiser_id']);
                    $data['platform'] = $platform;

                    $model = PlatformFactory::create($platform);
                    $model->execute($data);
                }
            } catch (Exception $e) {
                $this->rechargeFailureHandle($data, $e->getMessage());
            }

            $arrError = $this->isComplete($data['serial_number']);
            if (!empty($arrError)) {
                $this->sendMsg($arrError);
            }
        } catch (Exception $e) {
            Yii::error("业务序号：" . $data['serial_number'] . "，data：" . json_encode($data, JSON_UNESCAPED_UNICODE) . "，充值失败: " . $e->getMessage(), __METHOD__);
        }

        return true;
    }

    public static function addJob(array $data)
    {
        $data['create_time'] = time();
        $que = Yii::$app->que;
        $job = new static([
            'data' => $data
        ]);

        if (!$data['isTimeRecharge']) {
            $que->setImportant();
        }

        if ($que->has($job)) {
            return true;
        }

        $delay = BcHelper::sub($data['execute_time'], $data['create_time']);
        $que->delay($delay)->push($job);
    }

    private function rechargeFailureHandle(array $data, string $msg)
    {
        Yii::error("业务序号：" . $data['serial_number'] . "，data：" . json_encode($data, JSON_UNESCAPED_UNICODE) . "，充值失败: " . $msg, __METHOD__);

        $result[422] = [[
            'msg' => $msg,
            'target_advertiser_id' => $data['target_advertiser_id'],
        ]];

        // 更新充值记录
        $record = AdsTransferMoneyRecord::find()
            ->where(['id' => $data['record_id']])
            ->one();
        $record->platform = $data['platform'] ?? '';
        $record->status = AdsTransferMoneyRecordStatusEnum::FAILURE;
        $record->result = json_encode($result, JSON_UNESCAPED_UNICODE);
        if (!$record->save()) {
            Yii::error('更新充值记录失败: ' . json_encode($record->getErrors()), __METHOD__);
        }
    }

    private function isComplete($serialNumber)
    {
        $arrError = [];

        $count = AdsTransferMoneyRecord::find()
            ->where(['serial_number' => $serialNumber])
            ->andWhere(['status' => AdsTransferMoneyRecordStatusEnum::PENDING])
            ->count();

        if ((int)$count === 0) {
            $res = $this->getGroupedTransferResults($serialNumber);
            $arrError = $this->resRealData($res);
        }

        return $arrError;
    }

    private function resRealData($res)
    {
        $content = [];
        foreach ($res as $code => $v) {
            if ($code == 200) {
                $content[$code] = '充值成功';
            }
            if ($code == 201) {
                $mainBodyBalances = [];
                foreach ($v as $entry) {
                    $mainBody = $entry['main_body'] ?? '';
                    $balance = $entry['insufficientBalance'] ?? null;
                    $advertiserId = $entry['advertiser_id'] ?? '';

                    if ($mainBody && $balance !== null) {
                        if (!isset($mainBodyBalances[$mainBody])) {
                            $mainBodyBalances[$mainBody] = [
                                '余额' => $balance,
                                'advertiser_id' => $advertiserId,
                            ];
                        } else {
                            // 更新余额为最小值
                            $mainBodyBalances[$mainBody]['余额'] = min($mainBodyBalances[$mainBody]['余额'], $balance);
                        }
                    }
                }
                $index = 0; // 记录当前循环的索引
                $total = count($mainBodyBalances); // 总条目数
                foreach ($mainBodyBalances as $mainBody => $data) {
                    $content[$code] .= '主体：' . $mainBody . '，账户ID：' . $data['advertiser_id'] . ' 的备用金仅剩：' . $data['余额'] . '元';
                    if ($index < $total) {
                        $content[$code] .= PHP_EOL;
                    }
                }
            }

            if ($code != 200 && $code != 201) {
                $index = 0;
                $total = count($v);
                foreach ($v as $entry) {
                    $target_advertiser_name = $entry['target_advertiser_name'] ? '(' . $entry['target_advertiser_name'] . ')' : "";
                    $content[$code] .= '账户：' . $entry['target_advertiser_id'] . $target_advertiser_name . '，失败原因：' . $entry['msg'];
                    if ($index < $total) {
                        $content[$code] .= '<br/>';
                    }
                }
            }
        }

        $list = [];
        foreach ($content as $code => $msg) {
            $list[$code] = [
                'code' => $code,
                'msg' => $msg
            ];
        }

        return $list;
    }

    /**
     * 按照业务序号获取分组的充值结果
     * 
     * @param string $serialNumber 充值序号
     * @return array 转换后的分组结果
     */
    private function getGroupedTransferResults(string $serialNumber): array
    {
        // 从数据库查询相关记录
        $records = AdsTransferMoneyRecord::find()
            ->select(['id', 'result', 'serial_number', 'target_advertiser_id', 'status'])
            ->where(['serial_number' => $serialNumber])
            ->asArray()
            ->all();

        // 转换为分组格式
        return $this->convertRecordsToGroupedFormat($records);
    }

    /**
     * 将数据库中的充值结果记录转换为按错误码分组的格式
     * 
     * @param array $records 从数据库查询出的记录数组，每条记录包含result字段
     * @return array 按错误码分组的结果数组
     */
    private function convertRecordsToGroupedFormat(array $records): array
    {
        $groupedResult = [];

        foreach ($records as $record) {
            if ($record['status'] == AdsTransferMoneyRecordStatusEnum::SUCCESS && empty($record['result'])) {
                if (!isset($groupedResult[200])) {
                    $groupedResult[200] = [];
                }
                // 将成功的记录添加到200分组中
                $groupedResult[200][] = [
                    'msg' => '充值成功',
                    'target_advertiser_id' => $record['target_advertiser_id'],
                ];
                continue;
            }

            // 检查result字段是否存在且不为空
            if (empty($record['result'])) {
                continue;
            }

            try {
                // 解码JSON数据
                $resultData = json_decode($record['result'], true);

                // 检查JSON解码是否成功
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Yii::error('JSON解码失败: ' . json_last_error_msg() . ', 数据: ' . $record['result'], __METHOD__);
                    continue;
                }

                // 检查解码后的数据格式是否正确
                if (!is_array($resultData)) {
                    continue;
                }

                // 遍历每个错误码及其对应的数据
                foreach ($resultData as $code => $dataList) {
                    // 确保$dataList是数组
                    if (!is_array($dataList)) {
                        continue;
                    }

                    // 初始化该错误码的数组（如果不存在）
                    if (!isset($groupedResult[$code])) {
                        $groupedResult[$code] = [];
                    }

                    // 将当前记录的数据合并到对应的错误码分组中
                    // 由于您提到二维数组部分只会有一个数组元素，这里使用array_merge
                    $groupedResult[$code] = array_merge($groupedResult[$code], $dataList);
                }
            } catch (Exception $e) {
                Yii::error('处理充值结果记录时发生异常: ' . $e->getMessage() . ', 记录ID: ' . ($record['id'] ?? 'unknown'), __METHOD__);
                continue;
            }
        }

        return $groupedResult;
    }

    private function sendMsg($arrError)
    {
        if (!$this->isSendMessage) {
            return false;
        }
        $content = '';
        $index = 0;
        $total = count($arrError);
        $is_success = false;
        foreach ($arrError as $code => $value) {
            if ($index > 0 && $index < $total) {
                $content .= '<br/>';
            }
            if ($code == 200) {
                $is_success = true;
                continue;
            }
            if ($code == 201) {
                $title = '备用金余额不足' . PHP_EOL . PHP_EOL;
                $group = FeishuExamineService::arrGroup('GGGLGTQ');
                Yii::$app->feishuNotice->text($title . $value['msg'], $group['chat_id']);
                $is_success = true;
                continue;
            }

            $content .= $value['msg'];
        }
        $sendUnionId = static::getSendUnionId($this->data['user_name']);
        $msg = '';
        $title = $this->data['isTimeRecharge'] ? '定时充值' : '推广充值';
        $title_bg = 'green';
        if (empty($content) && $is_success) {
            $title .= '成功';
            $msg = '业务序号：' . $this->data['serial_number'] . '<br/>';
            $msg .= '充值账户：' . implode('、', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'];
        }

        if ($content && $is_success) {
            $title .= '-部分充值成功';
            $title_bg = 'blue';
            $msg = '业务序号：' . $this->data['serial_number'] . '<br/>';
            $msg .= '充值账户：' . implode('、', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'] . '<br/>';
            $msg .= '充值失败：' . '<br/>' . $content;
        }

        if ($content && !$is_success) {
            $title .= '失败';
            $title_bg = 'red';
            $msg = '业务序号：' . $this->data['serial_number'] . '<br/>';
            $msg .= '充值账户：' . implode('、', $this->data['target_advertiser_ids']) . '<br/>';
            $msg .= '充值金额：' . $this->data['amount'] . '<br/>';
            $msg .= '充值失败：' . '<br/>' . $content;
        }

        if ($sendUnionId && $msg) {
            static::sendMsgToProcess($title, $msg, $title_bg, $sendUnionId);
        } else {
            Yii::error('未找到用户 ' . ($this->data['user_name'] ?: '--') . ' 的 UnionId：' . ($sendUnionId ?: '--') . ' 或者消息内容为空，无法发送消息', 'RechargeJob');
            Yii::$app->feishuNotice->text('未找到用户 ' . ($this->data['user_name'] ?: '--') . ' 的 UnionId：' . ($sendUnionId ?: '--') . ' 或者消息内容为空，无法发送消息，消息内容：' . PHP_EOL . $msg);
        }
        return true;
    }

    public static function addFansJob($sub_advertiser_id)
    {
        $transferAccount = Config::getByName('transferAccount');
        $transferAccount = explode("\n", $transferAccount);

        if (empty($sub_advertiser_id) || !in_array($sub_advertiser_id, $transferAccount)) {
            return false;
        }

        $subAccountInfo = AdsAccountSub::find()
            ->alias('aas')
            ->select(['aas.responsible_id'])
            ->leftJoin(['aa' => AdsAccount::tableName()], 'aa.id = aas.td_id')
            ->where(['aas.sub_advertiser_id' => $sub_advertiser_id])
            ->asArray()
            ->one();

        $model = new AdsTransferMoneyRecord();
        $model->serial_number = AdsTransferMoneyRecord::generateSerialNumber();
        $model->user_id = $subAccountInfo['responsible_id'] ?: 0;
        $model->user_name = '系统自动充值';
        $model->target_advertiser_id = $sub_advertiser_id;
        $model->amount = self::AUTO_RECHARGE_AMOUNT;
        $model->status = AdsTransferMoneyRecordStatusEnum::PENDING;
        $model->is_auto = AdsTransferMoneyRecordIsAutoEnum::AUTO;
        $model->result = '';
        if (!$model->save()) {
            Yii::error(json_encode($model->getErrors()), __METHOD__);
            return false;
        }

        $transferData = [
            'serial_number' => $model->serial_number,
            'user_id' => $model->user_id,
            'user_name' => '系统自动充值',
            'target_advertiser_ids' => [$sub_advertiser_id],
            'target_advertiser_id' => $sub_advertiser_id,
            'amount' => self::AUTO_RECHARGE_AMOUNT,
            'record_id' => $model->id,
            'isTimeRecharge' => '',
            'execute_time' => time(),
        ];

        $que = Yii::$app->que;
        $job = new static([
            'data' => $transferData,
            'isSendMessage' => false
        ]);

        if ($que->has($job)) {
            return true;
        }

        if (!self::checkTransferMoneyCount($sub_advertiser_id)) {
            return false;
        }

        $que->push($job);
    }

    /**
     * 检查单个账户在一分钟内的充值次数,不能超过5次，且当天不允许在充值
     */
    public static function checkTransferMoneyCount($sub_advertiser_id)
    {
        $redis = Yii::$app->cache;
        $dayKey = 'AddFansTransferMoneyCount:' . date('Y-m-d');
        $dayAccount = $redis->get($dayKey);
        if ($dayAccount && in_array($sub_advertiser_id, $dayAccount)) {
            return false;
        }

        // 检查是否超过5次
        $count = AdsTransferMoneyRecord::getAddFansTransferCount($sub_advertiser_id);
        $count = intval($count);
        if ($count <= self::AUTO_RECHARGE_COUNT_LIMIT) {
            return true;
        }

        $dayDelayTime = strtotime(date('Y-m-d')) + 86400 - time();
        $dayAccount[] = $sub_advertiser_id;
        $redis->set($dayKey, $dayAccount, $dayDelayTime);

        if (in_array(YII_ENV, ['local', 'dev'])) {
            Yii::error('账户ID：' . $sub_advertiser_id . ' 加粉异常,在一分钟内充值超过' . self::AUTO_RECHARGE_COUNT_LIMIT . '次，已被限制充值', __METHOD__);
            return false;
        }

        $group = FeishuExamineService::arrGroup('GGGLGTQ');
        $error = '账户ID：' . $sub_advertiser_id . '<br/>';
        $error .= '加粉异常,在一分钟内充值超过5次，已被限制充值';
        Yii::$app->feishuNotice->text($error, $group['chat_id']);
        return false;
    }

    public static function getSendUnionId($user_name)
    {
        $user_id = Member::find()
            ->select('id')
            ->where(['username' => $user_name])->scalar();
        if (empty($user_id)) {
            return '';
        }

        return \common\services\feishu\UserService::getPromoterUnionIdByID($user_id);
    }

    public static function sendMsgToProcess($title, $content, $title_bg, $union_id)
    {
        $url = "https://open.feishu.cn/anycross/trigger/callback/MDdkYmE4YjY3NmEzMmY1YjQzMWRkMDYwNjRlYTE3ZmM1?title={$title}&content={$content}&title_bg={$title_bg}&unionid={$union_id}";
        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Content-Type' => 'application/json'
            ],
        ]);
        return json_decode($response->getBody()->getContents(), true);
    }
}
