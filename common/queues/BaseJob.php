<?php

namespace common\queues;

use Yii;
use Exception;
use yii\base\BaseObject;
use yii\queue\JobInterface;

abstract class BaseJob extends BaseObject implements JobInterface
{
    // 时间内限制的次数：默认0不限制
    public $times = 0;
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 60;
    // 延迟时间：单位秒，默认10
    public $delay = 10;
    public $jobId;
    public $retryTimes = 0;
    public $delayTimes = 0;
    //最多可延长执行次数
    public $maxDelayTimes = 10;

    public function __construct($params = [])
    {
        parent::__construct($params);
        $this->buildJobId();
    }

    protected function buildJobId()
    {
        $this->jobId = time() . uniqid();
    }

    public function execute($queue)
    {
        try {
            if ($this->checkIsLimit()) {
                throw new Exception('该任务被限流，延迟执行');
            }
            if (!$this->addRecord()) {
                throw new Exception('添加任务记录到redis时失败');
            }
            $ret = $this->run($queue);
        } catch (Exception $e) {
            $ret = false;
            echo $e->getMessage() . PHP_EOL;
        }

        if (!$ret) {
            $this->delay($queue);
        }
    }

    /**
     * 延迟执行
     *
     * @param $queue
     */
    public function delay($queue)
    {
        if ($this->delayTimes < $this->maxDelayTimes) {
            $this->buildJobId();
            $this->delayTimes++;
            $queue->delay($this->delay + $this->delayTimes * 2)->push($this);
        }
    }

    /**
     * 判断是否限流
     *
     * @return bool
     */
    public function checkIsLimit()
    {
        if (!$this->times) {
            return false;
        }

        $key = Yii::$app->cache->buildKey($this->getJobKey());
        $key = str_replace('\\', '\\\\', $key);
        $keys = Yii::$app->redis->keys("{$key}*");
        if (count($keys) >= $this->times) {
            return true;
        }

        return false;
    }

    /**
     * 添加执行记录
     *
     * @return bool
     */
    public function addRecord()
    {
        return Yii::$app->cache->set($this->getJobKey() . uniqid(), 1, $this->time);
    }

    /**
     * 获取任务key标识
     * @return string
     */
    public function getJobKey()
    {
        return static::class . ':';
    }

    /**
     * 执行
     *
     * @param Queue $queue
     * @return bool
     */
    abstract public function run($queue);
}
